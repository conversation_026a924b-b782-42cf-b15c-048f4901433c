import { View, Text, TouchableOpacity,StyleSheet } from 'react-native'
import React from 'react'

const MyButton = ({title,onPress}) => {
  return (
    <TouchableOpacity 
    activeOpacity={0.9}
    onPress={onPress}
    style={ styles.button}>
        <Text style={styles.text}>{title}</Text>
    </TouchableOpacity>
  )
}



export default MyButton

const styles=StyleSheet.create({
    button:{
       backgroundColor:"orange",
        paddingHorizontal:30,
        paddingVertical:10,
        borderRadius:10
    },
    text:{
         color:"white",
            fontSize:18,
            fontWeight:500
    }
})