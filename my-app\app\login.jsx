import { View, Text } from 'react-native'
import React from 'react'
import MyButton from '@/components/MyButton'
import { useRouter } from 'expo-router'
import { Image } from 'react-native'

const Login = () => {
    const router=useRouter();
    const HandleLogin=()=>{
      router.navigate("/signup")
    }
  return (
    <View style={{flex:1}}>
        <Image source={require("@/assets/images/LoginImg.png")}
        />
      <MyButton title={"Login"} onPress={HandleLogin}/>
    </View>
  )
}

export default Login