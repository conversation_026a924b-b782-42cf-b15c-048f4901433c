{"version": 3, "names": ["StyleSheet", "APPROX_APP_BAR_HEIGHT", "DEFAULT_DRAWER_WIDTH", "DRAWER_DEFAULT_WIDTH_WEB", "getDrawerWidthNative", "layout", "drawerStyle", "defaultWidth", "width", "flatten", "endsWith", "percentage", "Number", "replace", "isFinite", "getDrawerWidthWeb"], "sourceRoot": "../../../src", "sources": ["utils/getDrawerWidth.tsx"], "mappings": ";;AAAA,SAAyBA,UAAU,QAAwB,cAAc;AAEzE,MAAMC,qBAAqB,GAAG,EAAE;AAChC,MAAMC,oBAAoB,GAAG,GAAG;;AAEhC;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,mBAAmBF,qBAAqB,QAAQC,oBAAoB,KAAK;AAE1G,OAAO,SAASE,oBAAoBA,CAAC;EACnCC,MAAM;EACNC;AAIF,CAAC,EAAE;EACD,MAAMC,YAAY,GAChBF,MAAM,CAACG,KAAK,GAAGP,qBAAqB,IAAI,GAAG,GACvCI,MAAM,CAACG,KAAK,GAAGP,qBAAqB,GACpCC,oBAAoB;EAE1B,MAAM;IAAEM,KAAK,GAAGD;EAAa,CAAC,GAAGP,UAAU,CAACS,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,CAAC;EAEtE,IAAI,OAAOE,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;IACpD;IACA,MAAMC,UAAU,GAAGC,MAAM,CAACJ,KAAK,CAACK,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAElD,IAAID,MAAM,CAACE,QAAQ,CAACH,UAAU,CAAC,EAAE;MAC/B,OAAON,MAAM,CAACG,KAAK,IAAIG,UAAU,GAAG,GAAG,CAAC;IAC1C;EACF;EAEA,OAAO,OAAOH,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGD,YAAY;AACzD;AAEA,OAAO,SAASQ,iBAAiBA,CAAC;EAChCT;AAGF,CAAC,EAAU;EACT,MAAM;IAAEE;EAAM,CAAC,GAAGR,UAAU,CAACS,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,CAAC;EAEvD,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,GAAGA,KAAK,IAAI;EACrB;EAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EAEA,OAAOL,wBAAwB;AACjC", "ignoreList": []}