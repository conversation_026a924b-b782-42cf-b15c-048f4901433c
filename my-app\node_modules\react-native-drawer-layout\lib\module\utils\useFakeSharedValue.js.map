{"version": 3, "names": ["React", "FakeSharedValue", "_listeners", "Map", "constructor", "value", "_value", "addListener", "id", "listener", "set", "removeListener", "delete", "modify", "modifier", "undefined", "get", "values", "_isReanimatedSharedValue", "useFakeSharedValue", "sharedValue", "useRef", "current"], "sourceRoot": "../../../src", "sources": ["utils/useFakeSharedValue.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,MAAMC,eAAe,CAAC;EACpBC,UAAU,GAAG,IAAIC,GAAG,CAAkC,CAAC;EAGvDC,WAAWA,CAACC,KAAa,EAAE;IACzB,IAAI,CAACC,MAAM,GAAGD,KAAK;EACrB;EAEAE,WAAWA,CAACC,EAAU,EAAEC,QAAiC,EAAE;IACzD,IAAI,CAACP,UAAU,CAACQ,GAAG,CAACF,EAAE,EAAEC,QAAQ,CAAC;EACnC;EAEAE,cAAcA,CAACH,EAAU,EAAE;IACzB,IAAI,CAACN,UAAU,CAACU,MAAM,CAACJ,EAAE,CAAC;EAC5B;EAEAK,MAAMA,CAACC,QAAoC,EAAE;IAC3C,IAAI,CAACT,KAAK,GAAGS,QAAQ,KAAKC,SAAS,GAAGD,QAAQ,CAAC,IAAI,CAACT,KAAK,CAAC,GAAG,IAAI,CAACA,KAAK;EACzE;EAEAW,GAAGA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACX,KAAK;EACnB;EAEAK,GAAGA,CAACL,KAAa,EAAE;IACjB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;EAEA,IAAIA,KAAKA,CAACA,KAAa,EAAE;IACvB,IAAI,CAACC,MAAM,GAAGD,KAAK;IAEnB,KAAK,MAAMI,QAAQ,IAAI,IAAI,CAACP,UAAU,CAACe,MAAM,CAAC,CAAC,EAAE;MAC/CR,QAAQ,CAACJ,KAAK,CAAC;IACjB;EACF;EAEA,IAAIA,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,MAAM;EACpB;EAEAY,wBAAwB,GAAG,IAAI;AACjC;;AAEA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACd,KAAa,EAAmB;EACjE,MAAMe,WAAW,GAAGpB,KAAK,CAACqB,MAAM,CAAyB,IAAI,CAAC;EAE9D,IAAID,WAAW,CAACE,OAAO,KAAK,IAAI,EAAE;IAChCF,WAAW,CAACE,OAAO,GAAG,IAAIrB,eAAe,CAACI,KAAK,CAAC;EAClD;EAEA,OAAOe,WAAW,CAACE,OAAO;AAC5B", "ignoreList": []}