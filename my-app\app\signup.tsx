import { View, Text } from 'react-native'
import React from 'react'
import MyButton from '@/components/MyButton'
import { useRouter } from 'expo-router'

const Signup = () => {
    const router=useRouter();
    const HandleSignup=()=>{
      router.navigate("/")
    }
  return (
    <View style={{flex:1,justifyContent:"center",alignItems:"center"}}>
      <MyButton title={"Signup"} onPress={HandleSignup}/>
    </View>
  )
}

export default Signup