import MyButton from "@/components/MyButton";
import { Link ,useRouter} from "expo-router";
import { View,Text } from "react-native";
const index=()=>{

  const router=useRouter();

  const HandleContinue=()=>{
    
    router.navigate("/login");
  }
  return(
  <View style={{flex:1, justifyContent:"center",alignItems:"center"}}>
    <MyButton title={"Continue"} onPress={HandleContinue}/>
  </View>
  );
}
export default index;