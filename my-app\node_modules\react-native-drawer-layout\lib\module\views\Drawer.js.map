{"version": 3, "names": ["React", "StyleSheet", "View", "useLatestCallback", "DrawerProgressContext", "getDrawerWidthWeb", "useFakeSharedValue", "Overlay", "jsx", "_jsx", "jsxs", "_jsxs", "Drawer", "direction", "drawerPosition", "drawerStyle", "drawerType", "onClose", "onTransitionStart", "onTransitionEnd", "open", "overlayStyle", "overlayAccessibilityLabel", "renderDrawerContent", "children", "style", "drawerWidth", "progress", "useEffect", "value", "drawerRef", "useRef", "onTransitionStartLatest", "onTransitionEndLatest", "element", "current", "addEventListener", "removeEventListener", "isOpen", "isRight", "drawerTranslateX", "drawerAnimatedStyle", "transition", "transform", "contentTranslateX", "contentAnimatedStyle", "drawerElement", "ref", "styles", "drawer", "position", "zIndex", "width", "right", "left", "mainContent", "content", "onPress", "accessibilityLabel", "Provider", "container", "create", "flex", "flexDirection", "top", "bottom", "max<PERSON><PERSON><PERSON>", "backgroundColor"], "sourceRoot": "../../../src", "sources": ["views/Drawer.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AAGnD,SAASC,qBAAqB,QAAQ,mCAAgC;AACtE,SAASC,iBAAiB,QAAQ,4BAAyB;AAC3D,SAASC,kBAAkB,QAAQ,gCAA6B;AAChE,SAASC,OAAO,QAAQ,WAAW;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEpC,OAAO,SAASC,MAAMA,CAAC;EACrBC,SAAS,GAAG,KAAK;EACjBC,cAAc,GAAGD,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;EACvDE,WAAW;EACXC,UAAU,GAAG,OAAO;EACpBC,OAAO;EACPC,iBAAiB;EACjBC,eAAe;EACfC,IAAI;EACJC,YAAY;EACZC,yBAAyB;EACzBC,mBAAmB;EACnBC,QAAQ;EACRC;AACW,CAAC,EAAE;EACd,MAAMC,WAAW,GAAGrB,iBAAiB,CAAC;IACpCU;EACF,CAAC,CAAC;EAEF,MAAMY,QAAQ,GAAGrB,kBAAkB,CAACc,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EAEjDpB,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpBD,QAAQ,CAACE,KAAK,GAAGT,IAAI,GAAG,CAAC,GAAG,CAAC;EAC/B,CAAC,EAAE,CAACA,IAAI,EAAEO,QAAQ,CAAC,CAAC;EAEpB,MAAMG,SAAS,GAAG9B,KAAK,CAAC+B,MAAM,CAAO,IAAI,CAAC;EAE1C,MAAMC,uBAAuB,GAAG7B,iBAAiB,CAAC,MAAM;IACtDe,iBAAiB,GAAGE,IAAI,KAAK,KAAK,CAAC;EACrC,CAAC,CAAC;EAEF,MAAMa,qBAAqB,GAAG9B,iBAAiB,CAAC,MAAM;IACpDgB,eAAe,GAAGC,IAAI,KAAK,KAAK,CAAC;EACnC,CAAC,CAAC;EAEFpB,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,MAAMM,OAAO,GAAGJ,SAAS,CAACK,OAAgC;IAE1DD,OAAO,EAAEE,gBAAgB,CAAC,iBAAiB,EAAEJ,uBAAuB,CAAC;IACrEE,OAAO,EAAEE,gBAAgB,CAAC,eAAe,EAAEH,qBAAqB,CAAC;IAEjE,OAAO,MAAM;MACXC,OAAO,EAAEG,mBAAmB,CAAC,iBAAiB,EAAEL,uBAAuB,CAAC;MACxEE,OAAO,EAAEG,mBAAmB,CAAC,eAAe,EAAEJ,qBAAqB,CAAC;IACtE,CAAC;EACH,CAAC,EAAE,CAACA,qBAAqB,EAAED,uBAAuB,CAAC,CAAC;EAEpD,MAAMM,MAAM,GAAGtB,UAAU,KAAK,WAAW,GAAG,IAAI,GAAGI,IAAI;EACvD,MAAMmB,OAAO,GAAGzB,cAAc,KAAK,OAAO;EAE1C,MAAM0B,gBAAgB;EACpB;EACApB,IAAI,IAAIJ,UAAU,KAAK,MAAM,GACzBF,cAAc,KAAK,MAAM,GACvB,MAAM,GACN,OAAO,GACT,CAAC;EAEP,MAAM2B,mBAAmB,GACvBzB,UAAU,KAAK,WAAW,GACtB;IACE0B,UAAU,EAAE,gBAAgB;IAC5BC,SAAS,EAAE,cAAcH,gBAAgB;EAC3C,CAAC,GACD,IAAI;EAEV,MAAMI,iBAAiB,GAAGxB,IAAI;EAC1B;EACAJ,UAAU,KAAK,OAAO,GACpB,CAAC,GACD,QAAQU,WAAW,MAAMZ,cAAc,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAChE,CAAC;EAEL,MAAM+B,oBAAoB,GACxB7B,UAAU,KAAK,WAAW,GACtB;IACE0B,UAAU,EAAE,gBAAgB;IAC5BC,SAAS,EAAE,cAAcC,iBAAiB;EAC5C,CAAC,GACD,IAAI;EAEV,MAAME,aAAa,gBACjBrC,IAAA,CAACP,IAAI;IAEH6C,GAAG,EAAEjB,SAAU;IACfL,KAAK,EAAE,CACLuB,MAAM,CAACC,MAAM,EACb;MACEC,QAAQ,EAAElC,UAAU,KAAK,WAAW,GAAG,UAAU,GAAG,UAAU;MAC9DmC,MAAM,EAAEnC,UAAU,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG;IACvC,CAAC;IACD;IACA;MAAEoC,KAAK,EAAE1B;IAAY,CAAC;IACtB;IACAV,UAAU,KAAK,WAAW;IACtB;IACA;IACA;IACAF,cAAc,KAAK,OAAO,GACxB;MAAEuC,KAAK,EAAE,QAAQ3B,WAAW;IAAS,CAAC,GACtC;MAAE4B,IAAI,EAAE,QAAQ5B,WAAW;IAAS,CAAC,GACvC,IAAI,EACRe,mBAAmB,EACnB1B,WAAW,CACX;IAAAS,QAAA,EAEDD,mBAAmB,CAAC;EAAC,GAvBlB,QAwBA,CACP;EAED,MAAMgC,WAAW,gBACf5C,KAAA,CAACT,IAAI;IAAeuB,KAAK,EAAE,CAACuB,MAAM,CAACQ,OAAO,EAAEX,oBAAoB,CAAE;IAAArB,QAAA,gBAChEf,IAAA,CAACP,IAAI;MACH,eAAaoC,MAAM,IAAItB,UAAU,KAAK,WAAY;MAClDS,KAAK,EAAEuB,MAAM,CAACQ,OAAQ;MAAAhC,QAAA,EAErBA;IAAQ,CACL,CAAC,EACNR,UAAU,KAAK,WAAW,gBACzBP,IAAA,CAACF,OAAO;MACNa,IAAI,EAAEA,IAAK;MACXO,QAAQ,EAAEA,QAAS;MACnB8B,OAAO,EAAEA,CAAA,KAAMxC,OAAO,CAAC,CAAE;MACzBQ,KAAK,EAAEJ,YAAa;MACpBqC,kBAAkB,EAAEpC;IAA0B,CAC/C,CAAC,GACA,IAAI;EAAA,GAfA,SAgBJ,CACP;EAED,oBACEb,IAAA,CAACL,qBAAqB,CAACuD,QAAQ;IAAC9B,KAAK,EAAEF,QAAS;IAAAH,QAAA,eAC9Cb,KAAA,CAACT,IAAI;MAACuB,KAAK,EAAE,CAACuB,MAAM,CAACY,SAAS,EAAEnC,KAAK,CAAE;MAAAD,QAAA,GACpC,CAACe,OAAO,IAAIO,aAAa,EACzBS,WAAW,EACXhB,OAAO,IAAIO,aAAa;IAAA,CACrB;EAAC,CACuB,CAAC;AAErC;AAEA,MAAME,MAAM,GAAG/C,UAAU,CAAC4D,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB,CAAC;EACDd,MAAM,EAAE;IACNe,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,MAAM;IAChBC,eAAe,EAAE;EACnB,CAAC;EACDX,OAAO,EAAE;IACPM,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}