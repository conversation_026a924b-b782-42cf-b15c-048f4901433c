"use strict";

import { PlatformPressable, Text } from '@react-navigation/elements';
import { useTheme } from '@react-navigation/native';
import Color from 'color';
import * as React from 'react';
import { StyleSheet, View } from 'react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * A component used to show an action item with an icon and a label in a navigation drawer.
 */
export function DrawerItem(props) {
  const {
    colors,
    fonts
  } = useTheme();
  const {
    href,
    icon,
    label,
    labelStyle,
    focused = false,
    allowFontScaling,
    activeTintColor = colors.primary,
    // eslint-disable-next-line @eslint-react/no-unstable-default-props
    inactiveTintColor = Color(colors.text).alpha(0.68).rgb().string(),
    // eslint-disable-next-line @eslint-react/no-unstable-default-props
    activeBackgroundColor = Color(activeTintColor).alpha(0.12).rgb().string(),
    inactiveBackgroundColor = 'transparent',
    style,
    onPress,
    pressColor,
    pressOpacity = 1,
    testID,
    accessibilityLabel,
    ...rest
  } = props;
  const {
    borderRadius = 56
  } = StyleSheet.flatten(style || {});
  const color = focused ? activeTintColor : inactiveTintColor;
  const backgroundColor = focused ? activeBackgroundColor : inactiveBackgroundColor;
  const iconNode = icon ? icon({
    size: 24,
    focused,
    color
  }) : null;
  return /*#__PURE__*/_jsx(View, {
    collapsable: false,
    ...rest,
    style: [styles.container, {
      borderRadius,
      backgroundColor
    }, style],
    children: /*#__PURE__*/_jsx(PlatformPressable, {
      testID: testID,
      onPress: onPress,
      role: "button",
      "aria-label": accessibilityLabel,
      "aria-selected": focused,
      pressColor: pressColor,
      pressOpacity: pressOpacity,
      hoverEffect: {
        color
      },
      href: href,
      children: /*#__PURE__*/_jsxs(View, {
        style: [styles.wrapper, {
          borderRadius
        }],
        children: [iconNode, /*#__PURE__*/_jsx(View, {
          style: [styles.label, {
            marginStart: iconNode ? 12 : 0
          }],
          children: typeof label === 'string' ? /*#__PURE__*/_jsx(Text, {
            numberOfLines: 1,
            allowFontScaling: allowFontScaling,
            style: [styles.labelText, {
              color
            }, fonts.medium, labelStyle],
            children: label
          }) : label({
            color,
            focused
          })
        })]
      })
    })
  });
}
const styles = StyleSheet.create({
  container: {
    overflow: 'hidden'
  },
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 11,
    paddingStart: 16,
    paddingEnd: 24
  },
  label: {
    marginEnd: 12,
    marginVertical: 4,
    flex: 1
  },
  labelText: {
    lineHeight: 24,
    textAlignVertical: 'center'
  }
});
//# sourceMappingURL=DrawerItem.js.map