{"version": 3, "names": ["React", "DrawerStatusContext", "useDrawerStatus", "drawerStatus", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useDrawerStatus.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmB,QAAQ,0BAAuB;;AAE3D;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAiB;EAC9C,MAAMC,YAAY,GAAGH,KAAK,CAACI,UAAU,CAACH,mBAAmB,CAAC;EAE1D,IAAIE,YAAY,KAAKE,SAAS,EAAE;IAC9B,MAAM,IAAIC,KAAK,CACb,sEACF,CAAC;EACH;EAEA,OAAOH,YAAY;AACrB", "ignoreList": []}