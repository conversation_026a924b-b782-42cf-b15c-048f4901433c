{"version": 3, "names": ["Pressable", "StyleSheet", "Animated", "useAnimatedProps", "useAnimatedStyle", "jsx", "_jsx", "PROGRESS_EPSILON", "Overlay", "progress", "onPress", "style", "accessibilityLabel", "rest", "animatedStyle", "opacity", "value", "animatedProps", "active", "View", "styles", "overlay", "children", "pressable", "role", "accessible", "create", "absoluteFillObject", "backgroundColor", "flex", "pointerEvents"], "sourceRoot": "../../../src", "sources": ["views/Overlay.native.tsx"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,UAAU,QAAQ,cAAc;AACpD,OAAOC,QAAQ,IACbC,gBAAgB,EAChBC,gBAAgB,QACX,yBAAyB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAIjC,MAAMC,gBAAgB,GAAG,IAAI;AAE7B,OAAO,SAASC,OAAOA,CAAC;EACtBC,QAAQ;EACRC,OAAO;EACPC,KAAK;EACLC,kBAAkB,GAAG,cAAc;EACnC,GAAGC;AACS,CAAC,EAAE;EACf,MAAMC,aAAa,GAAGV,gBAAgB,CAAC,MAAM;IAC3C,OAAO;MACLW,OAAO,EAAEN,QAAQ,CAACO;IACpB,CAAC;EACH,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMQ,aAAa,GAAGd,gBAAgB,CAAC,MAAM;IAC3C,MAAMe,MAAM,GAAGT,QAAQ,CAACO,KAAK,GAAGT,gBAAgB;IAEhD,OAAO;MACL,eAAe,EAAEW,MAAM,GAAG,MAAM,GAAG,MAAM;MACzC,aAAa,EAAE,CAACA;IAClB,CAAC;EACH,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;EAEd,oBACEH,IAAA,CAACJ,QAAQ,CAACiB,IAAI;IAAA,GACRN,IAAI;IACRF,KAAK,EAAE,CAACS,MAAM,CAACC,OAAO,EAAEP,aAAa,EAAEH,KAAK,CAAE;IAC9CM,aAAa,EAAEA,aAAc;IAAAK,QAAA,eAE7BhB,IAAA,CAACN,SAAS;MACRU,OAAO,EAAEA,OAAQ;MACjBC,KAAK,EAAES,MAAM,CAACG,SAAU;MACxBC,IAAI,EAAC,QAAQ;MACb,cAAYZ,kBAAmB;MAC/Ba,UAAU;IAAA,CACX;EAAC,CACW,CAAC;AAEpB;AAEA,MAAML,MAAM,GAAGnB,UAAU,CAACyB,MAAM,CAAC;EAC/BL,OAAO,EAAE;IACP,GAAGpB,UAAU,CAAC0B,kBAAkB;IAChCC,eAAe,EAAE;EACnB,CAAC;EACDL,SAAS,EAAE;IACTM,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}